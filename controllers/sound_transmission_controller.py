import csv
import io
from flask import Blueprint, request, render_template, make_response, send_file
from services.sound_transmission_service import SoundTransmissionService
from utils.result import success, error, bad_request
from decorators import login_required

sound_transmission_bp = Blueprint('sound_transmission', __name__, url_prefix='/sound_transmission')
sound_transmission_service = SoundTransmissionService()

class SoundTransmissionController:
    """隔声量控制器"""
    
    @staticmethod
    @sound_transmission_bp.route('/transmission_loss_query')
    @login_required
    def transmission_loss_query_page():
        """垂直入射法隔声量查询页面"""
        return render_template('sound_transmission/transmission_loss_query.html')
    
    @staticmethod
    @sound_transmission_bp.route('/api/parts')
    @login_required
    def get_parts():
        """获取零件列表"""
        try:
            parts = sound_transmission_service.get_part_list()
            return success(parts)
        except Exception as e:
            return error(f"获取零件列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/materials')
    @login_required
    def get_materials():
        """获取材料列表"""
        try:
            part_name = request.args.get('part_name')
            materials = sound_transmission_service.get_material_list(part_name)
            return success(materials)
        except Exception as e:
            return error(f"获取材料列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/weights')
    @login_required
    def get_weights():
        """获取克重列表"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            
            if not part_name or not material_name:
                return bad_request("请提供零件名称和材料名称")
            
            weights = sound_transmission_service.get_weight_list(part_name, material_name)
            return success(weights)
        except Exception as e:
            return error(f"获取克重列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/transmission_data')
    @login_required
    def get_transmission_data():
        """获取隔声量数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            data = sound_transmission_service.get_transmission_data(part_name, material_name, weight)
            
            if not data:
                return error("未找到匹配的数据")
            
            return success(data, "数据获取成功")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"获取隔声量数据失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/export_csv')
    @login_required
    def export_csv():
        """导出CSV数据"""
        try:
            part_name = request.args.get('part_name')
            material_name = request.args.get('material_name')
            weight = request.args.get('weight')
            
            if not all([part_name, material_name, weight]):
                return bad_request("请提供完整的查询条件")
            
            weight = float(weight)
            csv_content = sound_transmission_service.export_data_to_csv(part_name, material_name, weight)
            
            if not csv_content:
                return error("未找到匹配的数据")
            
            # 创建响应
            output = io.BytesIO()
            output.write(csv_content.encode('utf-8-sig'))  # 使用UTF-8 BOM以支持Excel
            output.seek(0)
            
            filename = f"隔声量_{part_name}_{material_name}_{int(weight)}g_m2.csv"
            
            return send_file(
                output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"导出数据失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/manufacturers')
    @login_required
    def get_manufacturers():
        """获取厂家列表"""
        try:
            manufacturers = sound_transmission_service.get_manufacturer_list()
            return success(manufacturers)
        except Exception as e:
            return error(f"获取厂家列表失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/search')
    @login_required
    def search_data():
        """搜索数据"""
        try:
            filters = {
                'part_name': request.args.get('part_name'),
                'material_name': request.args.get('material_name'),
                'weight': request.args.get('weight'),
                'manufacturer_name': request.args.get('manufacturer_name')
            }
            
            # 移除空值
            filters = {k: v for k, v in filters.items() if v}
            
            if filters.get('weight'):
                filters['weight'] = float(filters['weight'])
            
            results = sound_transmission_service.search_data(filters)
            return success(results, f"找到 {len(results)} 条记录")
        except ValueError:
            return bad_request("克重参数格式错误")
        except Exception as e:
            return error(f"搜索失败: {str(e)}")
    
    @staticmethod
    @sound_transmission_bp.route('/api/statistics')
    @login_required
    def get_statistics():
        """获取统计信息"""
        try:
            stats = sound_transmission_service.get_statistics()
            return success(stats)
        except Exception as e:
            return error(f"获取统计信息失败: {str(e)}")

# 注册蓝图到应用
def register_sound_transmission_routes(app):
    """注册隔声量路由"""
    app.register_blueprint(sound_transmission_bp)
